<script lang="ts">
  import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "$lib/components/ui/card";
  import { cn } from "$lib/utils";
  import Navbar from "$lib/components/Navbar.svelte";
  import Footer from "$lib/components/Footer.svelte";
  import { Button } from "$lib/components/ui/button";
  
  let { className = "" } = $props();
  const playStoreLink = import.meta.env.VITE_PLAY_STORE_LINK;
  const appStoreLink = import.meta.env.VITE_APP_STORE_LINK;

  function handleAppStoreClick() {
    if(appStoreLink) {
      window.location.href = appStoreLink;
    } else {
      alert("App ancora non disponibile su App Store");
    }
  }

  function handlePlayStoreClick() {
    if(playStoreLink) {
      window.location.href = playStoreLink;
    } else {
      alert("App ancora non disponibile su Play Store");
    }
  }
</script>

<div class={cn("min-h-screen bg-background font-sans", className)}>
  <Navbar />
  
  <main class="container flex items-center justify-center min-h-[calc(100vh-3.5rem)] py-8">
    <Card class="w-full max-w-lg border-none shadow-lg">
      <CardHeader class="space-y-3">
        <CardTitle class="text-3xl font-medium tracking-tight text-center">
          Scarica la nostra App
        </CardTitle>
        <p class="text-muted-foreground text-lg text-center font-light">
          Scansiona il codice QR qui sotto per scaricare Il Salento sul tuo dispositivo mobile
        </p>
      </CardHeader>
      
      <CardContent class="flex flex-col items-center space-y-8">
        <div class="p-6 bg-white rounded-xl shadow-sm">
          <img 
            src="assets/il-salento-qrcode.jpg" 
            alt="QR Code for app download" 
            class="w-48 h-48 object-contain"
          />
        </div>

        <div class="flex gap-4 w-full max-w-md justify-center">
          <Button variant="ghost" class="p-0 h-auto" onclick={handleAppStoreClick}>
            <img 
              src="assets/app-store-badge.png"
              alt="Download on App Store"
              class="h-12 w-40 hover:opacity-80 transition-opacity duration-200"
            />
          </Button>
          <Button variant="ghost" class="p-0 h-auto" onclick={handlePlayStoreClick}>
            <img 
              src="assets/play-store-badge.png"
              alt="Get it on Play Store"
              class="h-12 w-40 hover:opacity-80 transition-opacity duration-200"
            />
          </Button>
        </div>
      </CardContent>
    </Card>
  </main>

  <Footer />
</div>
