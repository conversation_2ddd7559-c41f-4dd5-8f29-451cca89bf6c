import Home from './lib/pages/Home.svelte';
import Terms from './lib/pages/Terms.svelte';
import Privacy from './lib/pages/Privacy.svelte';
import Contacts from '$lib/pages/Contacts.svelte';
import Login from './lib/pages/amministrazione/Login.svelte';
import StoreRedirect from './lib/pages/StoreRedirect.svelte';
import Amministrazione from './lib/pages/amministrazione/Amministrazione.svelte';
import Categorie from './lib/pages/amministrazione/categorie/Categorie.svelte';
import Servizi from './lib/pages/amministrazione/servizi/Servizi.svelte';
import Comu<PERSON> from './lib/pages/amministrazione/comuni/Comuni.svelte';
import Eventi from './lib/pages/amministrazione/eventi/Eventi.svelte';
import NotFound from './lib/pages/NotFound.svelte';
import wrap from 'svelte-spa-router/wrap';
import { isAdminLogged } from './lib/utils';
import GiuntaPage from '$lib/pages/amministrazione/comuni/GiuntaPage.svelte';
import InEvidenza from '$lib/pages/amministrazione/in-evidenza/InEvidenza.svelte';
import ContrattoVisibilita from '$lib/pages/amministrazione/contratto-visibilita/ContrattoVisibilita.svelte';
import UserDetail from '$lib/pages/amministrazione/utenti/UserDetail.svelte';

export const routes = new Map();

/* Home page */
routes.set('/', Home);
routes.set('/terms', Terms);
routes.set('/privacy', Privacy);
routes.set('/contacts', Contacts);
routes.set('/store-redirect', StoreRedirect);

routes.set('/amministrazione', wrap({
    component: Amministrazione as any,
    userData: {
        fallbackUrl: '/amministrazione/login',
        message: 'Questa è una pagina riservata, effettua il login per accedere.',
    },
    conditions: [
        async () => {
            return await isAdminLogged();
        },
    ]
}));

routes.set('/amministrazione/login', wrap({
    component: Login as any,
    userData: {
        fallbackUrl: '/amministrazione'
    },
    conditions: [
        async () => {
            return !await isAdminLogged();
        },
    ]
}));

/* Admin routes */
routes.set('/amministrazione/categorie', wrap({
    component: Categorie as any,
    userData: {
        fallbackUrl: '/amministrazione/login',
        message: 'Questa è una pagina riservata, effettua il login per accedere.',
    },
    conditions: [
        async () => {
            return await isAdminLogged();
        },
    ]
}));

routes.set('/amministrazione/categorie/:id/servizi', wrap({
    component: Servizi as any,
    userData: {
        fallbackUrl: '/amministrazione/login',
        message: 'Questa è una pagina riservata, effettua il login per accedere.',
    },
    conditions: [
        async () => {
            return await isAdminLogged();
        },
    ]
}));

routes.set('/amministrazione/comuni', wrap({
    component: Comuni as any,
    userData: {
        fallbackUrl: '/amministrazione/login',
        message: 'Questa è una pagina riservata, effettua il login per accedere.',
    },
    conditions: [
        async () => {
            return await isAdminLogged();
        },
    ]
}));

routes.set('/amministrazione/comuni/:id/giunta', wrap({
    component: GiuntaPage as any,
    userData: {
        fallbackUrl: '/amministrazione/login',
        message: 'Questa è una pagina riservata, effettua il login per accedere.',
    },
    conditions: [
        async () => {
            return await isAdminLogged();
        },
    ]
}));

routes.set('/amministrazione/eventi', wrap({
    component: Eventi as any,
    userData: {
        fallbackUrl: '/amministrazione/login',
        message: 'Questa è una pagina riservata, effettua il login per accedere.',
    },
    conditions: [
        async () => {
            return await isAdminLogged();
        },
    ]
}));

routes.set('/amministrazione/in-evidenza', wrap({
    component: InEvidenza as any,
    userData: {
        fallbackUrl: '/amministrazione/login',
        message: 'Questa è una pagina riservata, effettua il login per accedere.',
    },
    conditions: [
        async () => {
            return await isAdminLogged();
        },
    ]
}));

routes.set('/amministrazione/contratto-visibilita', wrap({
    component: ContrattoVisibilita as any,
    userData: {
        fallbackUrl: '/amministrazione/login',
        message: 'Questa è una pagina riservata, effettua il login per accedere.',
    },
    conditions: [
        async () => {
            return await isAdminLogged();
        },
    ]
}));

routes.set('/amministrazione/utenti/:email', wrap({
    component: UserDetail as any,
    userData: {
        fallbackUrl: '/amministrazione/login',
        message: 'Questa è una pagina riservata, effettua il login per accedere.',
    },
    conditions: [
        async () => {
            return await isAdminLogged();
        },
    ]
}));

/* 404 - Should be the last route */
routes.set('*', NotFound);