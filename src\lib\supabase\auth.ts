import { supabase } from '$lib/supabase/supabaseClient';

export const authService = {
    async signIn(email: string, password: string) {
        const { data: { user, session }, error } = await supabase.auth.signInWithPassword({
            email,
            password
        });

        if (error) throw error;
        return { user, session };
    },

    async signOut() {
        const { error } = await supabase.auth.signOut();
        if (error) throw error;
    },

    async getCurrentUser() {
        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) throw error;
        return session?.user;
    },

    /**
     * Get all admin users (users with service_role)
     * Note: In Supabase, we can't directly query users by role through the client API
     * This function uses a workaround by querying the creato_da and modificato_da fields
     * from various tables to identify admin users
     */
    async getAdminUsers() {
        // Get unique emails from creato_da and modificato_da fields across tables
        const tables = ['categorie', 'comuni', 'eventi', 'servizi', 'giunte_comunali', 'indirizzi', 'orari'];
        const uniqueEmails = new Set<string>();

        for (const table of tables) {
            // Get unique creato_da values
            const { data: createdData, error: createdError } = await supabase
                .from(table)
                .select('creato_da')
                .not('creato_da', 'is', null);

            if (createdError) throw createdError;

            createdData?.forEach(row => {
                if (row.creato_da) uniqueEmails.add(row.creato_da);
            });

            // Get unique modificato_da values
            const { data: modifiedData, error: modifiedError } = await supabase
                .from(table)
                .select('modificato_da')
                .not('modificato_da', 'is', null);

            if (modifiedError) throw modifiedError;

            modifiedData?.forEach(row => {
                if (row.modificato_da) uniqueEmails.add(row.modificato_da);
            });
        }

        return Array.from(uniqueEmails);
    },

    /**
     * Get statistics for each admin user
     * @returns Array of user statistics with email, created count, and modified count
     */
    async getUserStatistics() {
        const tables = ['categorie', 'comuni', 'eventi', 'servizi', 'giunte_comunali', 'indirizzi', 'orari'];
        const adminUsers = await this.getAdminUsers();

        const userStats = adminUsers.map(email => ({
            email,
            created: 0,
            modified: 0
        }));

        for (const table of tables) {
            for (const userStat of userStats) {
                // Count records created by this user
                const { count: createdCount, error: createdError } = await supabase
                    .from(table)
                    .select('*', { count: 'exact', head: true })
                    .eq('creato_da', userStat.email);

                if (createdError) throw createdError;
                userStat.created += createdCount || 0;

                // Count records modified by this user
                const { count: modifiedCount, error: modifiedError } = await supabase
                    .from(table)
                    .select('*', { count: 'exact', head: true })
                    .eq('modificato_da', userStat.email);

                if (modifiedError) throw modifiedError;
                userStat.modified += modifiedCount || 0;
            }
        }

        return userStats;
    }
};