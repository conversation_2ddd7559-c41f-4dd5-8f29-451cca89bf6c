<script lang="ts">
  import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from "$lib/components/ui/dialog";
  import { Button } from "$lib/components/ui/button";
  import { Input } from "$lib/components/ui/input";
  import { Label } from "$lib/components/ui/label";
  import { CrudService } from "$lib/supabase/crud";
  import { toast } from "svelte-sonner";
  import type { Tables } from "$lib/supabase/database.types";

  type GiuntaComunale = Tables<'giunte_comunali'>;

  let { 
    open = false,
    comuneId,
    membro = null,
    onClose
  } = $props<{
    open: boolean;
    comuneId: number;
    membro?: GiuntaComunale | null;
    onClose: () => void;
  }>();

  $effect(() => {
    if (comuneId === 0) {
      onClose();
    }
  });

  const giuntaService = new CrudService('giunte_comunali');
  let saving = $state(false);
  let formData = $state<Record<string, any>>({
    nome: '',
    cognome: '',
    data_nascita: '',
    incarico: '',
    deleghe: '',
    biografia: '',
    sequenza_visualizzazione: 0
  });

  $effect(() => {
    if (membro) {
      formData = { ...membro };
    } else {
      formData = {
        nome: '',
        cognome: '',
        data_nascita: '',
        incarico: '',
        deleghe: '',
        biografia: '',
        sequenza_visualizzazione: 0,
        comune_id: comuneId
      };
    }
  });

  async function handleSubmit(e: SubmitEvent) {
    e.preventDefault();
    try {
      saving = true;
      if (membro) {
        await giuntaService.update(membro.id, formData);
        toast.success("Membro della giunta aggiornato con successo");
      } else {
        await giuntaService.create(formData);
        toast.success("Membro della giunta creato con successo");
      }
      onClose();
    } catch (error) {
      toast.error("Errore nel salvataggio del membro della giunta");
    } finally {
      saving = false;
    }
  }
</script>

<Dialog {open}>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>{membro ? "Modifica membro" : "Nuovo membro"}</DialogTitle>
      <DialogDescription>
        {membro ? "Modifica i dati del membro della giunta" : "Inserisci i dati del nuovo membro della giunta"}
      </DialogDescription>
    </DialogHeader>

    <form onsubmit={handleSubmit} class="space-y-4">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="space-y-2">
          <Label for="nome">Nome</Label>
          <Input id="nome" required bind:value={formData.nome} />
        </div>
        <div class="space-y-2">
          <Label for="cognome">Cognome</Label>
          <Input id="cognome" required bind:value={formData.cognome} />
        </div>
        <div class="space-y-2">
          <Label for="data_nascita">Data di nascita</Label>
          <Input id="data_nascita" type="date" bind:value={formData.data_nascita} />
        </div>
        <div class="space-y-2">
          <Label for="incarico">Incarico</Label>
          <Input id="incarico" required bind:value={formData.incarico} />
        </div>
        <div class="space-y-2 md:col-span-2">
          <Label for="deleghe">Deleghe</Label>
          <Input id="deleghe" bind:value={formData.deleghe} />
        </div>
        <div class="space-y-2 md:col-span-2">
          <Label for="biografia">Biografia</Label>
          <Input id="biografia" bind:value={formData.biografia} />
        </div>
        <div class="space-y-2">
          <Label for="sequenza">Sequenza visualizzazione</Label>
          <Input 
            id="sequenza" 
            type="number" 
            bind:value={formData.sequenza_visualizzazione} 
          />
        </div>
      </div>

      <DialogFooter>
        <Button type="button" variant="outline" onclick={onClose}>
          Annulla
        </Button>
        <Button type="submit" disabled={saving}>
          {saving ? "Salvataggio..." : "Salva"}
        </Button>
      </DialogFooter>
    </form>
  </DialogContent>
</Dialog> 