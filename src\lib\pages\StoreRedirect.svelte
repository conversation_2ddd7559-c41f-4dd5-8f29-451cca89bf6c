<script>
import { onMount } from 'svelte';

onMount(() => {
    const userAgent = navigator.userAgent;
    if (/iPad|iPhone|iPod/.test(userAgent)) {
        const appStoreLink = import.meta.env.VITE_APP_STORE_LINK;
    
        if(appStoreLink) {
        window.location.href = appStoreLink;
        } else {
        alert("App ancora non disponibile su App Store");
        }
    } else {
        const playStoreLink = import.meta.env.VITE_PLAY_STORE_LINK;

        if(playStoreLink) {
        window.location.href = playStoreLink;
        } else {
        alert("App ancora non disponibile su Play Store");
        }
    }
});
</script>