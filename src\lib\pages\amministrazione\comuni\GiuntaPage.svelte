<script lang="ts">
  import AdminLayout from "$lib/components/ui/layout/AdminLayout.svelte";
  import { CrudService } from "$lib/supabase/crud";
  import { toast } from "svelte-sonner";
  import type { Tables } from "$lib/supabase/database.types";
  import EntityDialog from "$lib/components/ui/dialog/EntityDialog.svelte";
  import DataTable from "$lib/components/ui/data-table/DataTable.svelte";
  import { Button } from "$lib/components/ui/button";
  import { Edit, Trash2, ArrowLeft, Upload } from "lucide-svelte";
  import { push } from "svelte-spa-router";
  import { Select, SelectTrigger, SelectContent, SelectItem } from "$lib/components/ui/select";
  import { StorageService } from '$lib/supabase/storage';
  import { Avatar, AvatarImage, AvatarFallback } from "$lib/components/ui/avatar/index.js";
  import { Dialog, DialogContent, DialogHeader, DialogTitle } from "$lib/components/ui/dialog";

  let { params } = $props<{ params: { id: string } }>();

  type GiuntaComunale = Tables<'giunte_comunali'>;
  type Comune = Tables<'comuni'>;

  const giuntaService = new CrudService('giunte_comunali');
  const comuniService = new CrudService('comuni');
  const storageService = new StorageService('giunte_comunali');

  let comune = $state<Comune | null>(null);
  let membri = $state<GiuntaComunale[]>([]);
  let loading = $state(true);
  let dialogOpen = $state(false);
  let editingMembro = $state<GiuntaComunale | null>(null);
  let saving = $state(false);
  let sortField = $state<keyof GiuntaComunale | null>(null);
  let sortDirection = $state<'asc' | 'desc'>('asc');
  let searchQuery = $state('');
  let filteredMembri = $derived(getFilteredMembri());
  let imageUploading = $state(false);
  let imagePreviewOpen = $state(false);
  let previewingImage = $state<string | null>(null);
  let imageTimestamps = $state<Record<string, number>>({});

  async function loadData() {
    try {
      loading = true;
      const comuneId = parseInt(params.id);
      if (isNaN(comuneId)) {
        toast.error("ID comune non valido");
        push('/amministrazione/comuni');
        return;
      }

      const [comuneData, membriData] = await Promise.all([
        comuniService.getById(comuneId),
        // Use getByField instead of getAll for more efficient querying
        giuntaService.getByField('comune_id', comuneId)
      ]);

      if (!comuneData) {
        toast.error("Comune non trovato");
        push('/amministrazione/comuni');
        return;
      }

      comune = comuneData;
      membri = membriData; // No need to filter as we're already getting only the relevant members
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error("Errore nel caricamento dei dati");
      push('/amministrazione/comuni');
    } finally {
      loading = false;
    }
  }

  $effect(() => {
    loadData();
  });

  function handleAdd() {
    editingMembro = null;
    dialogOpen = true;
  }

  function handleEdit(membro: GiuntaComunale) {
    editingMembro = membro;
    dialogOpen = true;
  }

  async function handleDelete(id: number) {
    try {
      await giuntaService.delete(id);
      toast.success("Membro eliminato con successo");
      await loadData();
    } catch (error) {
      toast.error("Errore nell'eliminazione del membro");
    }
  }

  async function handleSubmit(data: Record<string, any>) {
    try {
      saving = true;
      const payload = {
        ...data,
        comune_id: parseInt(params.id)
      };

      if (editingMembro) {
        await giuntaService.update(editingMembro.id, payload);
        toast.success("Membro aggiornato con successo");
      } else {
        await giuntaService.create(payload);
        toast.success("Membro creato con successo");
      }
      dialogOpen = false;
      await loadData();
    } catch (error) {
      toast.error("Errore nel salvataggio del membro");
    } finally {
      saving = false;
    }
  }

  function handleBack() {
    push('/amministrazione/comuni');
  }

  function handleSort(field: keyof GiuntaComunale) {
    if (sortField === field) {
      sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      sortField = field;
      sortDirection = 'asc';
    }
  }

  function getSortIcon(field: keyof GiuntaComunale) {
    if (sortField !== field) return '↕';
    return sortDirection === 'asc' ? '↑' : '↓';
  }

  function getSortOptions() {
    return [
      { value: 'cognome', label: 'Cognome' },
      { value: 'nome', label: 'Nome' },
      { value: 'incarico', label: 'Incarico' },
      { value: 'sequenza_visualizzazione', label: 'Sequenza' }
    ];
  }

  function getFilteredMembri(): GiuntaComunale[] {
    let filtered = !searchQuery.trim()
      ? membri
      : membri.filter(membro => {
          const query = searchQuery.toLowerCase();
          return (
            (membro.nome?.toLowerCase() || '').includes(query) ||
            (membro.cognome?.toLowerCase() || '').includes(query) ||
            (membro.incarico?.toLowerCase() || '').includes(query)
          );
        });

    if (sortField) {
      filtered = [...filtered].sort((a, b) => {
        const aValue = sortField ? a[sortField] : null;
        const bValue = sortField ? b[sortField] : null;

        if (aValue === null || aValue === undefined) return sortDirection === 'asc' ? 1 : -1;
        if (bValue === null || bValue === undefined) return sortDirection === 'asc' ? -1 : 1;

        const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        return sortDirection === 'asc' ? comparison : -comparison;
      });
    }

    return filtered;
  }

  function handleSearch(event: Event) {
    const target = event.target as HTMLInputElement;
    searchQuery = target.value;
  }

  async function handleImageUpload(event: Event, membro: GiuntaComunale) {
    const input = event.target as HTMLInputElement;
    const file = input.files?.[0];

    if (!file || !membro.id || !comune?.nome) return;

    try {
        imageUploading = true;
        const folderPath = `${comune.nome}/${membro.nome} ${membro.cognome}.jpg`;
        await storageService.uploadFile(file, folderPath);

        imageTimestamps[membro.id.toString()] = Date.now();

        await loadData();
        toast.success("Immagine caricata con successo");
    } catch (error) {
        console.error('Upload error:', error);
        toast.error("Errore nel caricamento dell'immagine");
    } finally {
        imageUploading = false;
        input.value = '';
    }
  }

  function getMemberImage(membro: GiuntaComunale) {
    if (!membro?.id || !comune?.nome) return '';
    const timestamp = imageTimestamps[membro.id.toString()] || '';
    const folderPath = `${comune.nome}/${membro.nome} ${membro.cognome}.jpg`;
    return storageService.getImageUrl(folderPath) + (timestamp ? `?t=${timestamp}` : '');
  }

  function handleImageClick(imageUrl: string) {
    previewingImage = imageUrl;
    imagePreviewOpen = true;
  }
</script>

<AdminLayout>
  <div class="mb-4">
    <Button variant="ghost" onclick={handleBack}>
      <ArrowLeft class="mr-2 h-4 w-4" />
      Torna ai comuni
    </Button>
  </div>

  <DataTable
    title={`Giunta Comunale - ${comune?.nome}`}
    description="Gestisci i membri della giunta comunale"
    onAdd={handleAdd}
    searchPlaceholder="Cerca membri..."
    onSearch={handleSearch}
  >
    {#snippet actions()}
      <Select
        type="single"
        value={sortField || ''}
        onValueChange={(value: string) => handleSort(value as keyof GiuntaComunale)}
      >
        <SelectTrigger class="w-[150px] bg-transparent border-none shadow-none hover:bg-accent hover:text-accent-foreground focus:ring-0">
          {sortField
            ? `${getSortOptions().find(opt => opt.value === sortField)?.label} ${sortDirection === 'asc' ? '↑' : '↓'}`
            : 'Ordina per...'
          }
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="">Nessun ordinamento</SelectItem>
          {#each getSortOptions() as option}
            <SelectItem value={option.value}>
              {option.label}
            </SelectItem>
          {/each}
        </SelectContent>
      </Select>
    {/snippet}

    <div class="relative overflow-x-auto">
      <!-- Mobile view (card layout) -->
      <div class="md:hidden space-y-4">
        {#if loading}
          <div class="p-4 text-center">Caricamento...</div>
        {:else if filteredMembri.length === 0}
          <div class="p-4 text-center">Nessun membro trovato</div>
        {:else}
          {#each filteredMembri as membro}
            <div class="bg-card rounded-lg shadow p-4 space-y-2">
              <div class="flex justify-between items-start">
                <div class="flex items-center gap-4">
                  <div class="relative">
                    <Avatar
                      class="h-12 w-12 cursor-pointer"
                      onclick={() => handleImageClick(getMemberImage(membro))}
                    >
                      <AvatarImage src={getMemberImage(membro)} alt={`${membro.nome} ${membro.cognome}`} />
                      <AvatarFallback>{membro.nome?.[0]?.toUpperCase()}{membro.cognome?.[0]?.toUpperCase()}</AvatarFallback>
                    </Avatar>
                  </div>
                  <div>
                    <h3 class="font-medium">{membro.nome} {membro.cognome}</h3>
                    <p class="text-sm text-muted-foreground">{membro.incarico}</p>
                  </div>
                </div>
                <div class="flex space-x-2">
                  <Button variant="ghost" size="icon" onclick={() => handleEdit(membro)}>
                    <Edit size={16} />
                  </Button>
                  <Button variant="ghost" size="icon" onclick={() => handleDelete(membro.id)}>
                    <Trash2 size={16} />
                  </Button>
                </div>
              </div>
              <div class="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span class="text-muted-foreground">Deleghe:</span><br />
                  {membro.deleghe || 'N/D'}
                </div>
                <div>
                  <span class="text-muted-foreground">Sequenza:</span><br />
                  {membro.sequenza_visualizzazione}
                </div>
                {#if membro.data_nascita}
                <div class="col-span-2">
                  <span class="text-muted-foreground">Data di nascita:</span><br />
                  {new Date(membro.data_nascita).toLocaleDateString('it-IT')}
                </div>
                {/if}
                {#if membro.biografia}
                <div class="col-span-2">
                  <span class="text-muted-foreground">Biografia:</span><br />
                  {membro.biografia}
                </div>
                {/if}
              </div>
            </div>
          {/each}
        {/if}
      </div>

      <!-- Desktop view (table layout) -->
      <table class="w-full text-sm text-left hidden md:table">
        <thead class="text-sm bg-muted">
          <tr>
            <th class="px-6 py-3">Immagine</th>
            <th class="px-6 py-3">
              <button
                class="flex items-center space-x-1 hover:text-primary"
                onclick={() => handleSort('cognome')}
              >
                <span>Cognome</span>
                <span class="text-muted-foreground">{getSortIcon('cognome')}</span>
              </button>
            </th>
            <th class="px-6 py-3">
              <button
                class="flex items-center space-x-1 hover:text-primary"
                onclick={() => handleSort('nome')}
              >
                <span>Nome</span>
                <span class="text-muted-foreground">{getSortIcon('nome')}</span>
              </button>
            </th>
            <th class="px-6 py-3">
              <button
                class="flex items-center space-x-1 hover:text-primary"
                onclick={() => handleSort('incarico')}
              >
                <span>Incarico</span>
                <span class="text-muted-foreground">{getSortIcon('incarico')}</span>
              </button>
            </th>
            <th class="px-6 py-3">Deleghe</th>
            <th class="px-6 py-3">
              <button
                class="flex items-center space-x-1 hover:text-primary"
                onclick={() => handleSort('sequenza_visualizzazione')}
              >
                <span>Sequenza</span>
                <span class="text-muted-foreground">{getSortIcon('sequenza_visualizzazione')}</span>
              </button>
            </th>
            <th class="px-6 py-3 text-right">Azioni</th>
          </tr>
        </thead>
        <tbody>
          {#if loading}
            <tr>
              <td colspan="6" class="px-6 py-4 text-center">Caricamento...</td>
            </tr>
          {:else if membri.length === 0}
            <tr>
              <td colspan="6" class="px-6 py-4 text-center">Nessun membro trovato</td>
            </tr>
          {:else}
            {#each filteredMembri as membro}
              <tr class="border-b hover:bg-muted/50">
                <td class="px-6 py-4">
                  <div class="relative inline-block">
                    <Avatar
                      class="h-12 w-12 cursor-pointer"
                      onclick={() => handleImageClick(getMemberImage(membro))}
                    >
                      <AvatarImage src={getMemberImage(membro)} alt={`${membro.nome} ${membro.cognome}`} />
                      <AvatarFallback>{membro.nome?.[0]?.toUpperCase()}{membro.cognome?.[0]?.toUpperCase()}</AvatarFallback>
                    </Avatar>
                  </div>
                </td>
                <td class="px-6 py-4">{membro.cognome}</td>
                <td class="px-6 py-4">{membro.nome}</td>
                <td class="px-6 py-4">{membro.incarico}</td>
                <td class="px-6 py-4">{membro.deleghe}</td>
                <td class="px-6 py-4">{membro.sequenza_visualizzazione}</td>
                <td class="px-6 py-4 text-right space-x-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onclick={() => handleEdit(membro)}
                  >
                    <Edit size={16} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onclick={() => handleDelete(membro.id)}
                  >
                    <Trash2 size={16} />
                  </Button>
                </td>
              </tr>
            {/each}
          {/if}
        </tbody>
      </table>
    </div>
  </DataTable>

  <EntityDialog
    open={dialogOpen}
    title={editingMembro ? "Modifica membro" : "Nuovo membro"}
    description={editingMembro ? "Modifica i dati del membro" : "Inserisci i dati del nuovo membro"}
    fields={[
      { key: "nome", label: "Nome", required: true, value: editingMembro?.nome ?? "" },
      { key: "cognome", label: "Cognome", required: true, value: editingMembro?.cognome ?? "" },
      { key: "incarico", label: "Incarico", required: true, value: editingMembro?.incarico ?? "" },
      { key: "deleghe", label: "Deleghe", value: editingMembro?.deleghe ?? "" },
      { key: "biografia", label: "Biografia", type: "textarea", value: editingMembro?.biografia ?? "" },
      { key: "data_nascita", label: "Data di nascita", type: "date", value: editingMembro?.data_nascita ?? "" },
      { key: "sequenza_visualizzazione", label: "Sequenza", type: "number", value: editingMembro?.sequenza_visualizzazione ?? 0 }
    ]}
    onSubmit={handleSubmit}
    onCancel={() => dialogOpen = false}
    loading={saving}
  />

  <Dialog open={imagePreviewOpen} onOpenChange={(open) => imagePreviewOpen = open}>
    <DialogContent class="max-w-3xl">
      <DialogHeader>
        <DialogTitle>Anteprima immagine</DialogTitle>
      </DialogHeader>
      <div class="flex flex-col gap-4">
        <img src={previewingImage} alt="Preview" class="w-full rounded-lg" />
        <div class="flex justify-center">
          <div>
            <input
              type="file"
              accept="image/jpeg"
              class="hidden"
              id="imageUpload"
              onchange={(e) => {
                const membro = membri.find(m => getMemberImage(m) === previewingImage);
                if (membro) {
                  handleImageUpload(e, membro);
                  imagePreviewOpen = false;
                }
              }}
              disabled={imageUploading}
            />
            <Button
              variant="outline"
              class="gap-2"
              type="button"
              onclick={() => document.getElementById('imageUpload')?.click()}
            >
              <Upload class="h-4 w-4" />
              Sostituisci
            </Button>
          </div>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</AdminLayout>