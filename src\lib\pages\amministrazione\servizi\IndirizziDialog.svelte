<script lang="ts">
  import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "$lib/components/ui/dialog";
  import { Button } from "$lib/components/ui/button";
  import { Input } from "$lib/components/ui/input";
  import { Label } from "$lib/components/ui/label";
  import { CrudService } from "$lib/supabase/crud";
  import { toast } from "svelte-sonner";
  import type { Tables } from "$lib/supabase/database.types";

  type Indirizzo = Tables<'indirizzi'>;

  let {
    open = false,
    servizioId,
    indirizzo = null,
    onClose
  } = $props<{
    open: boolean;
    servizioId: number;
    indirizzo?: Indirizzo | null;
    onClose: () => void;
  }>();

  $effect(() => {
    if (servizioId === 0) {
      onClose();
    }
  });

  const indirizziService = new CrudService('indirizzi');
  let saving = $state(false);
  let formData = $state<Record<string, any>>({
    via: '',
    numero_civico: '',
    cap: '',
    città: '',
    provincia: '',
    comune: '',
    latitude: null,
    longitude: null
  });

  $effect(() => {
    if (indirizzo) {
      formData = { ...indirizzo };
    } else {
      formData = {
        via: '',
        numero_civico: '',
        cap: '',
        città: '',
        provincia: '',
        comune: '',
        latitude: null,
        longitude: null,
        servizio_id: servizioId
      };
    }
  });

  async function handleSubmit(e: SubmitEvent) {
    e.preventDefault();
    try {
      saving = true;
      if (indirizzo) {
        await indirizziService.update(indirizzo.id, formData);
        toast.success("Indirizzo aggiornato con successo");
      } else {
        await indirizziService.create(formData);
        toast.success("Indirizzo creato con successo");
      }
      onClose();
    } catch (error) {
      toast.error("Errore nel salvataggio dell'indirizzo");
    } finally {
      saving = false;
    }
  }
</script>

<Dialog {open} onOpenChange={(isOpen) => !isOpen && onClose()}>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>{indirizzo ? "Modifica indirizzo" : "Nuovo indirizzo"}</DialogTitle>
      <DialogDescription>
        {indirizzo ? "Modifica i dati dell'indirizzo" : "Inserisci i dati del nuovo indirizzo"}
      </DialogDescription>
    </DialogHeader>

    <form onsubmit={handleSubmit} class="space-y-4">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="space-y-2">
          <Label for="via">Via</Label>
          <Input id="via" required bind:value={formData.via} />
        </div>
        <div class="space-y-2">
          <Label for="numero_civico">Numero civico</Label>
          <Input id="numero_civico" required bind:value={formData.numero_civico} />
        </div>
        <div class="space-y-2">
          <Label for="cap">CAP</Label>
          <Input id="cap" required bind:value={formData.cap} />
        </div>
        <div class="space-y-2">
          <Label for="città">Città</Label>
          <Input id="città" required bind:value={formData.città} />
        </div>
        <div class="space-y-2">
          <Label for="provincia">Provincia</Label>
          <Input id="provincia" required bind:value={formData.provincia} />
        </div>
        <div class="space-y-2">
          <Label for="comune">Comune</Label>
          <Input id="comune" required bind:value={formData.comune} />
        </div>
        <div class="space-y-2">
          <Label for="latitude">Latitudine</Label>
          <Input id="latitude" type="number" step="any" bind:value={formData.latitude} />
        </div>
        <div class="space-y-2">
          <Label for="longitude">Longitudine</Label>
          <Input id="longitude" type="number" step="any" bind:value={formData.longitude} />
        </div>
      </div>

      <DialogFooter>
        <Button type="button" variant="outline" onclick={onClose}>
          Annulla
        </Button>
        <Button type="submit" disabled={saving}>
          {saving ? "Salvataggio..." : "Salva"}
        </Button>
      </DialogFooter>
    </form>
  </DialogContent>
</Dialog>