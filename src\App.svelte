<script lang="ts">
  import Router from "svelte-spa-router";
  import { replace } from 'svelte-spa-router';
  import { routes } from "./routes";
  import { Toaster } from "$lib/components/ui/sonner";
  import { toast } from "svelte-sonner";

  const conditionsFailed = (event: any) => {
    if (event.detail.userData.message)
      toast.error(event.detail.userData.message);
    
		replace(event.detail.userData.fallbackUrl);
	}
</script>

<main>
  <Router {routes} on:conditionsFailed={conditionsFailed}/>
  <Toaster />
</main>

<style>
</style>
