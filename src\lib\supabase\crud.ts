import { supabase } from './supabaseClient';
import type { Database } from './database.types';
import { getCurrentUserEmail } from '../utils';

type TableName = keyof Database['public']['Tables'];
type Row<T extends TableName> = Database['public']['Tables'][T]['Row'];
type Insert<T extends TableName> = Database['public']['Tables'][T]['Insert'];
type Update<T extends TableName> = Database['public']['Tables'][T]['Update'];

export class CrudService<T extends TableName> {
  constructor(private readonly table: T) {}

  async getAll(): Promise<Row<T>[]> {
    // Supabase has a default limit of 1000 records per query
    // We increase the limit to ensure we get all records
    const { data, error } = await supabase
      .from(this.table)
      .select('*')
      .limit(10000); // Increased limit to 10000

    if (error) throw error;
    return (data ?? []) as unknown as Row<T>[];
  }

  async getById(id: number): Promise<Row<T>> {
    const { data, error } = await supabase
      .from(this.table)
      .select('*')
      .eq('id', id)
      .single();

    if (error) throw error;
    return data as unknown as Row<T>;
  }

  async create(data: Insert<T>): Promise<Row<T>> {
    // Add the current user's email to the creato_da field
    const userEmail = await getCurrentUserEmail();
    const dataWithCreator = {
      ...data,
      creato_da: userEmail
    };

    const { data: newData, error } = await supabase
      .from(this.table)
      .insert([dataWithCreator as any])
      .select()
      .single();

    if (error) throw error;
    return newData as unknown as Row<T>;
  }

  async update(id: number, data: Update<T>): Promise<Row<T>> {
    // Add the current user's email and current timestamp to the modification fields
    const userEmail = await getCurrentUserEmail();
    const dataWithModifier = {
      ...data,
      modificato_da: userEmail,
      modificato_il: new Date().toISOString()
    };

    const { data: updatedData, error } = await supabase
      .from(this.table)
      .update(dataWithModifier as any)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return updatedData as unknown as Row<T>;
  }

  async delete(id: number): Promise<void> {
    const { error } = await supabase
      .from(this.table)
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  // Method to get records by a specific field value
  // This is more efficient than fetching all records and filtering in the frontend
  async getByField(field: string, value: any): Promise<Row<T>[]> {
    const { data, error } = await supabase
      .from(this.table)
      .select('*')
      .eq(field, value)
      .limit(10000);

    if (error) throw error;
    return (data ?? []) as unknown as Row<T>[];
  }
}

// Usage example:
// const annunciService = new TableService('annunci');
// const categorieService = new TableService('categorie');