<script lang="ts">
  import { Label as LabelPrimitive } from "bits-ui";
  import { cn } from "$lib/utils";

  let { class: className, children, for: htmlFor } = $props<{
    class?: string;
    children?: any;
    for?: string;
  }>();
</script>

<LabelPrimitive.Root
  class={cn(
    "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
    className
  )}
  for={htmlFor}
>
  {@render children()}
</LabelPrimitive.Root> 