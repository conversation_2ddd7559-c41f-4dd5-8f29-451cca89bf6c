import PizZip from 'pizzip';
import Docxtemplater from 'docxtemplater';
import { saveAs } from 'file-saver';

export interface ContractData {
  identificativo: string;
  nomeCliente: string;
  cognomeCliente: string;
  nomeAzienda: string;
  ragioneSociale: string;
  viaCliente: string;
  numeroCivicoCliente: string;
  capCliente: string;
  paeseCliente: string;
  provinciaCliente: string;
  telefonoCliente: string;
  emailOPecCliente: string;
  pIvaOCfCliente: string;
  paeseInserzione: string;
  paesiAddizionaliInserzione: string;
  categorieInserzione: string;
  indirizzoInserzione: string;
  telefonoInserzione: string;
  emailOPecInserzione: string;
  sitoWebInserzione: string;
  descrizioneAziendaInserzione: string;
  orariAperturaInserzione: string;
  noteInserzione: string;
  costoMensile: string;
  costoAnnuale: string;
  dataContratto: string;
}

export class ContractService {
  private templateUrl: string;

  constructor(templateUrl: string) {
    this.templateUrl = templateUrl;
  }

  private async loadTemplate(): Promise<ArrayBuffer> {
    const response = await fetch(this.templateUrl);
    return await response.arrayBuffer();
  }

  async generateContract(data: ContractData): Promise<Blob> {
    try {
      const templateContent = await this.loadTemplate();
      const zip = new PizZip(templateContent);
      
      const doc = new Docxtemplater(zip, {
        paragraphLoop: true,
        linebreaks: true,
      });

      // Set the data and render
      doc.render(data);
      
      return doc.getZip().generate({
        type: 'blob',
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      });
    } catch (error) {
      console.error('Error generating contract:', error);
      throw error;
    }
  }

  async downloadContract(data: ContractData, filename: string) {
    const doc = await this.generateContract(data);
    saveAs(doc, filename);
  }
}