<script lang="ts">
  import { Dialog as DialogPrimitive } from "bits-ui";
  import { cn } from "$lib/utils";

  let { class: className, children } = $props<{
    class?: string;
    children?: any;
  }>();
</script>

<DialogPrimitive.Portal>
  <DialogPrimitive.Overlay 
    class="fixed inset-0 z-50 bg-black/80"
  />
  <DialogPrimitive.Content
    class={cn(
      "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 sm:rounded-lg",
      className
    )}
  >
    {@render children()}
  </DialogPrimitive.Content>
</DialogPrimitive.Portal> 