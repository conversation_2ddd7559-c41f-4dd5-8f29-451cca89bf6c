import { supabase } from './supabaseClient';

export class StorageService {
  constructor(private readonly bucket: string) {}

  private getPublicUrl(path: string) {
    const { data } = supabase.storage
      .from(this.bucket)
      .getPublicUrl(path);
    return data.publicUrl;
  }

  async uploadFile(file: File, path: string) {
    const { error } = await supabase.storage
      .from(this.bucket)
      .upload(path, file, {
        upsert: true,
        cacheControl: '3600'
      });

    if (error) throw error;
    return this.getPublicUrl(path);
  }

  async deleteFile(path: string) {
    const { error } = await supabase.storage
      .from(this.bucket)
      .remove([path]);

    if (error) throw error;
  }

  getImageUrl(path: string) {
    return this.getPublicUrl(path);
  }

  async listFiles(folderPath: string) {
    const { data, error } = await supabase.storage
      .from(this.bucket)
      .list(folderPath);

    if (error) throw error;
    return data || [];
  }

  async getServicePhotos(serviceName: string) {
    try {
      const files = await this.listFiles(serviceName);

      // Filter for JPG files that match the pattern serviceName-N.jpg
      const photoFiles = files
        .filter(file => file.name.endsWith('.jpg') && file.name.startsWith(`${serviceName}-`))
        .sort((a, b) => {
          // Extract number from filename (e.g., "ServiceName-1.jpg" -> 1)
          const getNumber = (filename: string) => {
            const match = filename.match(/-(\d+)\.jpg$/);
            return match ? parseInt(match[1]) : 0;
          };
          return getNumber(a.name) - getNumber(b.name);
        });

      return photoFiles.map(file => ({
        name: file.name,
        url: this.getImageUrl(`${serviceName}/${file.name}`),
        number: this.extractPhotoNumber(file.name)
      }));
    } catch (error) {
      console.error('Error listing service photos:', error);
      return [];
    }
  }

  private extractPhotoNumber(filename: string): number {
    const match = filename.match(/-(\d+)\.jpg$/);
    return match ? parseInt(match[1]) : 1;
  }

  async getNextPhotoNumber(serviceName: string): Promise<number> {
    try {
      const photos = await this.getServicePhotos(serviceName);
      if (photos.length === 0) return 1;

      const maxNumber = Math.max(...photos.map(photo => photo.number));
      return maxNumber + 1;
    } catch (error) {
      console.error('Error getting next photo number:', error);
      return 1;
    }
  }
}