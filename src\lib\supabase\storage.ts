import { supabase } from './supabaseClient';

export class StorageService {
  constructor(private readonly bucket: string) {}

  private getPublicUrl(path: string) {
    const { data } = supabase.storage
      .from(this.bucket)
      .getPublicUrl(path);
    return data.publicUrl;
  }

  async uploadFile(file: File, path: string) {
    const { error } = await supabase.storage
      .from(this.bucket)
      .upload(path, file, {
        upsert: true,
        cacheControl: '3600'
      });

    if (error) throw error;
    return this.getPublicUrl(path);
  }

  async deleteFile(path: string) {
    const { error } = await supabase.storage
      .from(this.bucket)
      .remove([path]);
    
    if (error) throw error;
  }

  getImageUrl(path: string) {
    return this.getPublicUrl(path);
  }
} 