import type { Database } from '$lib/supabase/database.types';

// Define table types using the Database type
export type Annuncio = Database['public']['Tables']['annunci']['Row'];
export type Categoria = Database['public']['Tables']['categorie']['Row'];
export type Comune = Database['public']['Tables']['comuni']['Row'];
export type Configurazione = Database['public']['Tables']['configurazioni']['Row'];
export type Evento = Database['public']['Tables']['eventi']['Row'];
export type GiuntaComunale = Database['public']['Tables']['giunte_comunali']['Row'];
export type Indirizzo = Database['public']['Tables']['indirizzi']['Row'];
export type Orario = Database['public']['Tables']['orari']['Row'];
export type Servizio = Database['public']['Tables']['servizi']['Row'];

// Insert types for creating new records
export type AnnuncioInsert = Database['public']['Tables']['annunci']['Insert'];
export type CategoriaInsert = Database['public']['Tables']['categorie']['Insert'];
export type ComuneInsert = Database['public']['Tables']['comuni']['Insert'];
export type ConfigurazioneInsert = Database['public']['Tables']['configurazioni']['Insert'];
export type EventoInsert = Database['public']['Tables']['eventi']['Insert'];
export type GiuntaComunaleInsert = Database['public']['Tables']['giunte_comunali']['Insert'];
export type IndirizzoInsert = Database['public']['Tables']['indirizzi']['Insert'];
export type OrarioInsert = Database['public']['Tables']['orari']['Insert'];
export type ServizioInsert = Database['public']['Tables']['servizi']['Insert'];

// Update types for modifying existing records
export type AnnuncioUpdate = Database['public']['Tables']['annunci']['Update'];
export type CategoriaUpdate = Database['public']['Tables']['categorie']['Update'];
export type ComuneUpdate = Database['public']['Tables']['comuni']['Update'];
export type ConfigurazioneUpdate = Database['public']['Tables']['configurazioni']['Update'];
export type EventoUpdate = Database['public']['Tables']['eventi']['Update'];
export type GiuntaComunaleUpdate = Database['public']['Tables']['giunte_comunali']['Update'];
export type IndirizzoUpdate = Database['public']['Tables']['indirizzi']['Update'];
export type OrarioUpdate = Database['public']['Tables']['orari']['Update'];
export type ServizioUpdate = Database['public']['Tables']['servizi']['Update'];